import React, { useMemo } from 'react';
import { map } from 'lodash';

import GqlFullCrudTable from '../../../../common/components/dataViews/GqlFullCrudTable';
import { IFilterBarComponentProps } from '../../../../common/components/controls/FilterBar/FilterBar';

import TenantsSelector from '../../common/filterbar/TenantsSelector';
import textToAudioEventsQuery from '../data/textToAudioEvents.graphql';
import textToAudioEventsCountQuery from '../data/textToAudioEventsCount.graphql';

import columns from './columns';
import useTenants from '../../../../common/data/hooks/useTenants';
import Spinner from '../../../../common/components/utils/Spinner';
import TextToAudioPlatform from '../../model/TextToAudioPlatform';
import { SELECTOR_TO_BE_HIDDEN_OPTIONS_COUNT } from '../../common/const';
import TextToAudioPlatformSelector from '../../common/filterbar/TextToAudioPlatformSelector';
import useT from '../../../../common/components/utils/Translations/useT';
import patchMasterLogTableColumns from '../../common/utils/patchMasterLogTableColumns';

const initialPlatformIds = map(TextToAudioPlatform.All, 'id');

const TextToAudioUsageHistoryTable = () => {
  const t = useT();
  const { tenants = [], loading, tenantById } = useTenants({
    sortKey: 'clientAbbreviation',
  });
  const initialTenantsValues = useMemo(() => map(tenants, 'id'), [tenants]);
  const hasMultipleTenants =
    initialTenantsValues?.length > SELECTOR_TO_BE_HIDDEN_OPTIONS_COUNT;

  const _columns = useMemo(() => {
    const cols = columns(t);
    if (!hasMultipleTenants) {
      return cols;
    }

    return patchMasterLogTableColumns(cols, {
      id: 'tenant_id',
      title: 'URL',
      minWidth: 200,
      sortable: false,
      cellRenderer: item => tenantById[item?.tenantId]?.siteLink,
    });
  }, [tenantById, hasMultipleTenants, t]);

  const filterComponent = useMemo(() => {
    const component = hasMultipleTenants
      ? {
          tenantIds: TenantsSelector,
          platformIds: TextToAudioPlatformSelector,
        }
      : {
          platformIds: TextToAudioPlatformSelector,
        };

    return component as {
      [key: string]: React.ComponentType<IFilterBarComponentProps<any>>;
    };
  }, [hasMultipleTenants]);

  if (loading) {
    return <Spinner />;
  }

  return (
    <GqlFullCrudTable
      hasFilters
      hasPagination
      gql={{
        query: textToAudioEventsQuery,
        count: textToAudioEventsCountQuery,
      }}
      list={{
        filterComponent,
        initialFilter: {
          tenantIds: initialTenantsValues,
          platformIds: initialPlatformIds,
          sortKey: 'created_at',
          sortOrder: 'desc',
        },
        tableConfig: {
          columns: _columns,
        },
      }}
      title={t('Text-To-Audio Usage History')}
    />
  );
};

export default TextToAudioUsageHistoryTable;
