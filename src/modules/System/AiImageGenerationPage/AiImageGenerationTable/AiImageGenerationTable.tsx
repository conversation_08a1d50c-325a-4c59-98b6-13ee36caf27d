import React, { useCallback, useMemo } from 'react';
import { isEqual, map } from 'lodash';
import moment from 'moment';

import GqlFullCrudTable from '../../../../common/components/dataViews/GqlFullCrudTable';
import { IFilterBarComponentProps } from '../../../../common/components/controls/FilterBar/FilterBar';

import TenantsSelector from '../../common/filterbar/TenantsSelector';
import getAiImageGenerationLogsGql from '../data/getAiImageGenerationLogs.graphql';
import getAiImageGenerationLogsCountGql from '../data/getAiImageGenerationLogsCount.graphql';

import columns from './columns';
import useTenants from '../../../../common/data/hooks/useTenants';
import Spinner from '../../../../common/components/utils/Spinner';
import AIImageGenerationLogStatus from '../../model/AIImageGenerationLogStatus';
import { SELECTOR_TO_BE_HIDDEN_OPTIONS_COUNT } from '../../common/const';
import useT from '../../../../common/components/utils/Translations/useT';
import patchMasterLogTableColumns from '../../common/utils/patchMasterLogTableColumns';
import AIImageGenerationLogStatusSelector from '../../common/filterbar/AIImageGenerationLogStatusSelector';
import EContentLogDateRangeSelector from '../../../../common/components/controls/FilterBar/EContentLogDateRangeSelector';
import { format, TransportFormat } from '../../../../common/utils/dateTime';
import useFilterStore from '../../../../common/components/controls/FilterBar/hooks/useFilterStore';
import useCurrentUser from '../../../../common/data/hooks/useCurrentUser';

const DEFAULT_LAST_DAYS = 30;

const initialStatuses = map(AIImageGenerationLogStatus.All, 'id');

const AiImageGenerationTable = () => {
  const t = useT();
  const {
    me: { tenantId },
  } = useCurrentUser();
  const { tenants = [], loading, tenantById } = useTenants({
    sortKey: 'clientAbbreviation',
  });
  const initialTenantsValues = useMemo(() => map(tenants, 'id'), [tenants]);
  const hasMultipleTenants =
    initialTenantsValues?.length > SELECTOR_TO_BE_HIDDEN_OPTIONS_COUNT;

  const initialLogDates = useMemo(() => {
    const now = moment();
    return {
      startDate: format(
        moment(now).subtract(DEFAULT_LAST_DAYS, 'days'),
        TransportFormat,
      ),
      endDate: format(moment(now), TransportFormat),
    };
  }, []);

  const {
    loading: fLoading,
    values: defaultFilterValues,
    onChange: onDefaultFilterValuesChange,
  } = useFilterStore('AI_IMAGE_GENERATION_LOGS_FILTER', {
    defaultValues: {
      status: initialStatuses,
      logDates: initialLogDates,
      sortKey: 'created_at',
      sortOrder: 'desc',
    },
  });
  const _columns = useMemo(() => {
    const cols = columns(t);
    if (!hasMultipleTenants) {
      return cols;
    }

    return patchMasterLogTableColumns(cols, {
      id: 'tenant_id',
      title: 'URL',
      minWidth: 200,
      sortable: false,
      cellRenderer: item => tenantById[item?.tenantId]?.siteLink,
    });
  }, [tenantById, hasMultipleTenants, t]);

  const filterComponent = useMemo(() => {
    const component = hasMultipleTenants
      ? {
          tenantIds: TenantsSelector,
          logDates: EContentLogDateRangeSelector,
          status: AIImageGenerationLogStatusSelector,
        }
      : {
          logDates: EContentLogDateRangeSelector,
          status: AIImageGenerationLogStatusSelector,
        };

    return component as {
      [key: string]: React.ComponentType<IFilterBarComponentProps<any>>;
    };
  }, [hasMultipleTenants]);

  const onFilterChange = useCallback(
    filters => {
      if (!isEqual(filters, defaultFilterValues)) {
        onDefaultFilterValuesChange({
          ...filters,
          startDate: filters?.logDates?.startDate,
          endDate: filters?.logDates?.endDate,
        });
      }
    },
    [onDefaultFilterValuesChange, defaultFilterValues],
  );

  const isListItemQueryPrevented = useCallback(
    variables => !variables.startDate || !variables.endDate,
    [],
  );

  if (loading) {
    return <Spinner />;
  }

  return (
    <GqlFullCrudTable
      hasFilters
      hasPagination
      gql={{
        query: getAiImageGenerationLogsGql,
        count: getAiImageGenerationLogsCountGql,
        isListItemQueryPrevented,
      }}
      list={{
        filterComponent,
        onFilterChange,
        initialFilter: {
          tenantIds: [tenantId],
          status: initialStatuses,
          ...defaultFilterValues,
          sortKey: 'created_at',
        },
        tableConfig: {
          columns: _columns,
        },
      }}
      title={t('AI Image Generation')}
    />
  );
};

export default AiImageGenerationTable;
