import toEdanaDisplayTimestamp4 from '../../../../common/utils/edanaDisplayTimestamp4';
import { Failed, Successfull } from '../../model/AIImageGenerationLogStatus';

export default function columns(t) {
  return [
    {
      id: 'created_at',
      title: 'Generation Date',
      minWidth: 240,
      tdClassName: 'pl-5',
      cellRenderer: item => toEdanaDisplayTimestamp4(item.createdAt),
    },
    {
      id: 'person_id',
      title: 'User',
      minWidth: 200,
      cellRenderer: item =>
        `${item.personId} ${item.user.firstName} ${item.user.lastName} (${item.user.contacts[0]?.contact})`,
    },
    {
      id: 'status',
      title: 'Generation Status',
      minWidth: 200,
      sortable: false,
      cellRenderer: ({ status, failErrorMessage }) => {
        if (status === Successfull.id) {
          return t(Successfull.name);
        }
        return `${t(Failed.name)} (${failErrorMessage})`;
      },
    },
  ];
}
