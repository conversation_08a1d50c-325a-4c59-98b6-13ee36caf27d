.arrowContainer {
  display: flex;
  justify-content: space-between;
  &:hover {
    div .arrow {
      opacity: 1;
    }
  }
}

.hasPrev {
  h6 {
    transition: padding-left 0.2s ease-in;
  }

  &:hover {
    h6 {
      padding-left: 24px;
    }
  }
}
.h5 {
  line-height: normal;
  margin-bottom: 3px;
}
.noSpaceBetween {
  justify-content: flex-start !important;
}
.hasPrev {
  h5 {
    transition: padding-left 0.2s ease-in;
  }

  &:hover {
    h5 {
      padding-left: 32px;
    }
  }

  /* Mobile screen styles */
  @media (max-width: 1024px) {
    &:hover {
      h5 {
        padding-left: 23px;
      }
    }
  }
}
.arrow {
  z-index: 100;
  color: #999;
  opacity: 0;
  transition: all 0.2s ease-in;
  i {
    font-size: 20px;
  }
}

@mixin wrapper {
  display: block;
  height: 100%;
  width: 20px;
  z-index: 100;
}

.right {
  margin-left: 8px;
  @include wrapper;
}

.left {
  position: absolute;
  left: 6px;
  @include wrapper;
}
.mg2 {
  margin-top: 2px !important;
  line-height: normal;
}
