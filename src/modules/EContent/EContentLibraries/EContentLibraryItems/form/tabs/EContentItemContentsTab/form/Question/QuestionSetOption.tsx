/* eslint-disable react/forbid-dom-props */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { get, map, trim, isEmpty, forEach, filter, isNumber } from 'lodash';
import classnames from 'classnames';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

import { getAlphaNumeric } from '../../../../../../../../../common/utils/mathHelpers';
import TextField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextField';
import NumberField from '../../../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import useEntityFormContext from '../../../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import useT from '../../../../../../../../../common/components/utils/Translations/useT';
import styles from './QuestionSetOption.scss';
import CheckboxField from '../../../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import Radio from '../../../../../../../../../common/components/controls/base/Radio';
import AttachmentField from '../../../../../../../../../common/components/containers/EntityForm/fields/AttachmentField';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../../fsCategories';
import useCurrentUser from '../../../../../../../../../common/data/hooks/useCurrentUser';
import EntityFormArrayField, {
  IEntityFormArrayFieldContentFields,
} from '../../../../../../../../../common/components/containers/EntityForm/EntityFormArrayField';
import IconButton from '../../../../../../../../../common/components/controls/IconButton';
import {
  TRUEFALSE,
  MULTI_TRUEFALSE,
  MULTI_SELECT,
  NUMBER,
} from '../../../../../../../../../model/QuestionType';
import TrueFalseMulti from '../../../../../../../../../model/TrueFalseMulti';
import SelectBoxField from '../../../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import AttachmentFieldWithPreview from '../../../../../../../../../common/components/containers/EntityForm/fields/AttachmentFieldWithPreview';
import AttachmentItemCard from '../../../../../../../../EdCom/common/AttachmentItemCard';
import { IMAGES } from '../../../../../../../../../common/propTypes';
import TextAreaField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import AddAttachmentStrike from '../../../../../../../../../common/components/controls/Attachments/addComponents/AddAttachmentStrike';
import Icon from '../../../../../../../../../common/components/utils/Icon';
import { ONE } from '../../../../../../../../../common/const';
import useStorageSettings from '../../../../../../../../../common/components/controls/base/TextEditor/useStorageSettings';

const TEN = 10;
const MAX_INT = 2000000000;
const MOBILE_BREAKPOINT = 444;
const points = [
  {
    name: 0,
    value: 0,
  },
  {
    name: 1,
    value: 1,
  },
  {
    name: 2,
    value: 2,
  },
  {
    name: 3,
    value: 3,
  },
  {
    name: 4,
    value: 4,
  },
  {
    name: 5,
    value: 5,
  },
  {
    name: 6,
    value: 6,
  },
  {
    name: 7,
    value: 7,
  },
  {
    name: 8,
    value: 8,
  },
  {
    name: 9,
    value: 9,
  },
  {
    name: 10,
    value: 10,
  },
];

export const useMobileDetection = (breakpoint = MOBILE_BREAKPOINT) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= breakpoint);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= breakpoint);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [breakpoint]);

  return isMobile;
};

const QuestionSetOption = ({
  isMultiple,
  refreshPage,
  setCorrectNumber,
}: {
  isMultiple: boolean;
  refreshPage: boolean;
  setCorrectNumber: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const isMobile = useMobileDetection();
  const t = useT();
  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();
  const { values, isEditing, setFieldValue } = useEntityFormContext();
  const currentType = useMemo(() => get(values, `questionType`, 0), [values]);
  const questionPoint = useMemo(() => get(values, `questionPoint`, undefined), [
    values,
  ]);
  const currentMultiple = useMemo(() => get(values, `questionMultiple`, 0), [
    values,
  ]);
  const optionslist = useMemo(() => get(values, `options`, []), [values]);
  const [isMount, setIsMount] = useState(true);
  const [autoHeight, setAutoHeight] = useState(true);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  useEffect(() => {
    if (isMount) {
      setIsMount(false);
      return;
    }
    currentType !== MULTI_TRUEFALSE.id && resetRadio();
  }, [isMultiple, currentType]);

  useEffect(() => {
    setCorrectNumber(
      filter(get(values, `options`, []), item => item.correctAnswer === true)
        .length,
    );
  }, [values]);

  const resetRadio = useCallback(() => {
    if (optionslist.length > 0) {
      map(optionslist, ({ description }, index) => {
        description && setFieldValue(`options.${index}.correctAnswer`, false);
      });
    }
  }, [optionslist]);
  const handleCancelField = useCallback(
    (fields, index) => () => {
      fields?.remove(index as number);
      fields.push({});
    },
    [],
  );

  const addAttachmentImage = useCallback(
    ({ ...props }) => <AddAttachmentStrike {...props} name="image" />,
    [t],
  );

  const handleRemoveField = useCallback(
    (fields, index) => () => {
      fields?.remove(index);
    },
    [t],
  );
  const handleDialogOpen = useCallback(() => setIsImageDialogOpen(true), []);
  const handleDialogClose = useCallback(() => setIsImageDialogOpen(false), []);

  const [showActionsMemberIndex, setShowActionsMemberIndex] = useState<
    number | undefined
  >(undefined);

  const handleRadioChange = useCallback(
    (member: string, index: number) => () => {
      if (isEditing) {
        resetRadio();
        setFieldValue(`${member}.correctAnswer`, true);
      }
      setShowActionsMemberIndex(index);
    },
    [isEditing, optionslist],
  );
  const onPaste = (event, name) => {
    let data = event.clipboardData.getData('text');

    data = data ? data.replace(/\n\s*\n/g, '\n').split(/\r?\n/) : 0;
    if (data.length > 1) {
      setAutoHeight(false);
      const start = name ? parseInt(name.match(/\[(.*)\]/).pop()) : 0;
      const delay = 500;
      forEach(data, (element, key) => {
        setFieldValue(`options.${start + key}.description`, element.trim());
      });
      setTimeout(() => {
        setFieldValue(`options.${start}.description`, data[0].trim());
        setAutoHeight(true);
      }, delay);
    }
  };
  const getItemStyle = (isDragging, draggableStyle) => ({
    // some basic styles to make the items look a bit nicer
    userSelect: 'none',
    width: '100%',
    // styles we need to apply on draggables
    ...draggableStyle,
  });
  const renderTickBox = useCallback(
    (member: string, index: number, isLast: boolean, isEditing: boolean) => {
      const correctAnswer = get(values, `${member}.correctAnswer`, '');
      if (
        isEditing &&
        (optionslist.length === 1 || isLast) &&
        currentType !== TRUEFALSE.id
      ) {
        return (
          <div
            className={classnames(
              currentType === MULTI_TRUEFALSE.id
                ? styles.selectBox
                : styles.tickBox,
            )}
          >
            {' '}
          </div>
        );
      }
      if (currentType === MULTI_TRUEFALSE.id) {
        return (
          <div className={classnames(styles.selectBox, 'pull-left')}>
            <SelectBoxField
              columns={1}
              itemTitlePropName="name"
              itemValuePropName="id"
              name={`${member}.correctAnswer`}
              options={TrueFalseMulti.Basic}
            />
          </div>
        );
      } else if (currentMultiple || currentType === MULTI_SELECT.id) {
        return (
          <div className={classnames(styles.tickBox)}>
            <CheckboxField name={`${member}.correctAnswer`} />
          </div>
        );
      }
      return (
        <div className={classnames(styles.tickBox, 'mb-5')}>
          <div className="col-lg-4 col-md-6 col-sm-12">
            <Radio
              key={index}
              value={correctAnswer === true}
              onChange={handleRadioChange(member, index)}
            />
          </div>
        </div>
      );
    },
    [currentType, currentMultiple, values, optionslist],
  );
  const renderInputBox = useCallback(
    (member: string, index: number) => {
      if (currentType === NUMBER.id) {
        const placeholder = t('Enter correct answer as a number');
        return (
          <NumberField
            noLabel
            columns={1}
            max={MAX_INT}
            name={`${member}.description`}
            placeholder={placeholder}
            precision={4}
            required={false}
            step={0.5}
            onPaste={onPaste}
          />
        );
      } else if (currentType === TRUEFALSE.id) {
        const placeholder = t('Enter option');
        const description = get(values, `${member}.description`, '');
        if (isEmpty(trim(description))) {
          const defaultValue = index === 0 ? 'True' : 'False';
          setFieldValue(`${member}.description`, defaultValue);
        }
        return (
          <TextField
            noLabel
            columns={1}
            maxLength={1000}
            name={`${member}.description`}
            placeholder={placeholder}
            required={false}
            onPaste={onPaste}
          />
        );
      }
      const placeholder =
        currentType === MULTI_TRUEFALSE.id
          ? t('Enter true/false question')
          : t('Enter option');
      return (
        <TextAreaField
          noLabel
          autoHeight={autoHeight}
          columns={1}
          maxLength={1000}
          name={`${member}.description`}
          placeholder={placeholder}
          required={false}
          rows={1}
          onPaste={onPaste}
        />
      );
    },
    [currentType, currentMultiple, values],
  );

  const { maxSize } = useStorageSettings(E_CONTENT_CONTENT_FILE, tenantId);

  const renderArrayFieldAddDeleteActions = useCallback(
    ({
      isEditing,
      fields,
      member,
      index,
      isHover,
      isLast,
      hidden,
    }: {
      isEditing?: boolean;
      fields: IEntityFormArrayFieldContentFields;
      member: string;
      index: number;
      isHover: boolean;
      isLast?: boolean;
      hidden?: boolean;
    }) => (
      <div
        className={classnames(styles.dFlex, {
          hidden: hidden || (isMobile && showActionsMemberIndex !== index),
        })}
      >
        <AttachmentField
          isListHidden
          addAttachmentComponent={addAttachmentImage}
          addButtonTitle={t('Add Image')}
          allowedFileTypes={IMAGES}
          allowedNumberOfFiles={1}
          categoryKey={E_CONTENT_CONTENT_FILE}
          columns={1}
          maxSize={maxSize}
          name={`${member}.attachments`}
          organisationGroupId={organisationGroupId}
          strikeIcon="image"
          tenantId={tenantId}
          onClose={handleDialogClose}
          onOpen={handleDialogOpen}
        />
        <IconButton
          hasBackground={false}
          iconClassName={classnames(styles.removeIcon)}
          iconName="cross2"
          title={t('Remove Option')}
          onClick={handleRemoveField(fields, index)}
        />
      </div>
    ),
    [
      maxSize,
      isMobile,
      showActionsMemberIndex,
      handleRemoveField,
      t,
      addAttachmentImage,
      handleDialogClose,
      handleDialogOpen,
      organisationGroupId,
      tenantId,
    ],
  );

  const renderFieldsLabel = useCallback(
    (index: number) => (index === 0 ? t('True') : t('False')),
    [t],
  );
  const attachmentItemComponent = useCallback(
    props => <AttachmentItemCard hideCaption hideDetail {...props} />,
    [],
  );

  const max2 = 2;
  const max1 = 1;
  const max10 = 10;
  const maxItemsCount = currentType === TRUEFALSE.id ? max2 : max10;
  const minItemsCount = currentType === TRUEFALSE.id ? max2 : max1;
  const _draggableDisable = !(isEditing && currentType !== TRUEFALSE.id);
  const onDragEnd = useCallback(
    result => {
      // dropped outside the list or last item
      if (
        !result.destination ||
        optionslist.length - max1 === result.destination.index
      ) {
        return;
      }
      const overrideList = optionslist.map(item => {
        if (isEmpty(item.description)) {
          item.description = '';
        }
        return item;
      });
      const newList = reorder(
        overrideList,
        result.source.index,
        result.destination.index,
      );
      setFieldValue(`options`, newList);
    },
    [optionslist],
  );
  const reorder = useCallback(
    (list: [], startIndex: number, endIndex: number) => {
      const result = Array.from(list);
      const [removed] = result.splice(startIndex, 1);
      result.splice(endIndex, 0, removed);
      return result;
    },
    [],
  );

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="droppable">
        {(provided, snapshot) => (
          <table
            ref={provided.innerRef}
            className={classnames(styles.fullWidth)}
          >
            <tbody>
              <EntityFormArrayField
                deletable
                hasAutoAddEmptyFields
                isNativeRemoveButtonHidden
                isTableArray
                maxItemsCount={maxItemsCount}
                minItemsCount={minItemsCount}
                name="options"
                removeButtonClassName={styles.removeBtn}
                rowWrapper={!!isEditing}
                wrapperClass={
                  currentType === TRUEFALSE.id || !isEditing
                    ? classnames(styles.marginLabel)
                    : ''
                }
              >
                {(
                  member,
                  index,
                  fields,
                  { isEditing },
                  { last },
                  draggable,
                  removeButton,
                  { isHover },
                ) => {
                  const drodownBox =
                    currentType === MULTI_TRUEFALSE.id ? styles.drodownBox : '';
                  return (
                    <Draggable
                      key={index}
                      draggableId={member + index}
                      index={index}
                      isDragDisabled={_draggableDisable}
                    >
                      {(provided, snapshot) => (
                        <tr
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          style={getItemStyle(
                            snapshot.isDragging,
                            provided.draggableProps.style,
                          )}
                        >
                          <td
                            className={classnames(
                              isEditing ? styles.dragRow : styles.dragRowView,
                            )}
                          >
                            <div
                              className={classnames(
                                drodownBox,
                                styles.wrapper,
                                {
                                  [styles.alignItemsNormal]:
                                    currentType === MULTI_TRUEFALSE.id,
                                },
                              )}
                            >
                              <div
                                className={classnames(styles.dragBox)}
                                // eslint-disable-next-line react/jsx-no-bind
                                onClick={() => setShowActionsMemberIndex(index)}
                              >
                                {isHover &&
                                  fields.length !== 1 &&
                                  !last &&
                                  !_draggableDisable && (
                                    <Icon
                                      badge="drag_indicator"
                                      name="material-icons"
                                      type="material"
                                    />
                                  )}
                              </div>
                              <div className={classnames(styles.orderBox)}>
                                <div
                                  className={classnames(
                                    styles.alphaOrder,
                                    'mb-10',
                                  )}
                                >
                                  {currentType !== TRUEFALSE.id &&
                                  (isEditing
                                    ? optionslist.length === ONE || last
                                    : false)
                                    ? ' '
                                    : getAlphaNumeric(index)}
                                </div>
                              </div>

                              {renderTickBox(
                                member,
                                index,
                                !!last,
                                isEditing || false,
                              )}
                              <div
                                className={classnames(
                                  isEditing
                                    ? styles.inputBox
                                    : styles.inputBoxView,
                                  [TRUEFALSE.id, NUMBER.id].includes(
                                    currentType,
                                  ) && styles.inputBoxText,
                                )}
                                // eslint-disable-next-line react/jsx-no-bind
                                onClick={() => setShowActionsMemberIndex(index)}
                              >
                                {renderInputBox(member, index)}
                              </div>
                              <div
                                className={classnames(
                                  styles.pointsSelect,
                                  'col-lg-2 col-md-2 col-sm-12 col-xs-7',
                                )}
                              >
                                {(currentType === MULTI_SELECT.id ||
                                  currentType === MULTI_TRUEFALSE.id) &&
                                  (isEditing ||
                                    isNumber(
                                      optionslist[index].questionPoint,
                                    )) && (
                                    <SelectBoxField
                                      isEmptyValueAllowed
                                      buttonClassNames={styles.pointsField}
                                      columns={6}
                                      disabled={!!questionPoint}
                                      name={`${member}.questionPoint`}
                                      options={points}
                                      placeholder={t('Points')}
                                      returnType="number"
                                      wrapperClassNames={{
                                        6: `col-lg-12 col-md-12 col-sm-12 col-xs-12`,
                                      }}
                                    />
                                  )}
                              </div>

                              <div
                                className={classnames(
                                  currentType === TRUEFALSE.id
                                    ? styles.optionBoxLabel
                                    : styles.optionBox,
                                )}
                              >
                                {currentType === TRUEFALSE.id
                                  ? renderFieldsLabel(index)
                                  : (isImageDialogOpen &&
                                      renderArrayFieldAddDeleteActions({
                                        isEditing,
                                        member,
                                        index,
                                        fields,
                                        isHover,
                                        isLast: last,
                                        hidden: true,
                                      })) ||
                                    ((isMobile || isHover) &&
                                      isEditing &&
                                      renderArrayFieldAddDeleteActions({
                                        isEditing,
                                        member,
                                        index,
                                        fields,
                                        isHover,
                                        isLast: last,
                                      }))}
                              </div>
                            </div>
                            <div className={classnames(styles.attachmentBox)}>
                              {!refreshPage && (
                                <AttachmentFieldWithPreview
                                  hideAdd
                                  allowedFileTypes={IMAGES}
                                  attachmentItemComponent={
                                    attachmentItemComponent
                                  }
                                  cardsInRow={4}
                                  categoryKey={E_CONTENT_CONTENT_FILE}
                                  columns={1}
                                  name={`${member}.attachments`}
                                  organisationGroupId={organisationGroupId}
                                  strikeTitle={' '}
                                  tenantId={tenantId}
                                />
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </Draggable>
                  );
                }}
              </EntityFormArrayField>
              {provided.placeholder}
            </tbody>
          </table>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default QuestionSetOption;
