/* eslint-disable react/forbid-dom-props */

import classnames from 'classnames';
import _ from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';

import EplusPropTypes from '../../../propTypes';
import withWindowScroll from '../../../utils/withWindowScroll';
import Icon from '../../utils/Icon';
import DropdownMenu from './DropdownMenu';

const MAX_PAGE_WIDTH = 409;

@withWindowScroll
export default class Dropdown extends React.PureComponent {
  static propTypes = {
    actions: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        linkPath: PropTypes.string,
        icon: EplusPropTypes.icon,
        iconSrc: PropTypes.string,
        onClick: PropTypes.func,
        hasStrike: PropTypes.bool,
        disabled: PropTypes.bool,
        itemWrapperClassName: PropTypes.string,
        linkClassName: PropTypes.string,
      }),
    ),
    data: PropTypes.any,
    footerRenderer: PropTypes.func,
    selectedId: PropTypes.number,
    isStrikeAction: PropTypes.bool,
    hasLargeWidth: PropTypes.bool,
    position: PropTypes.oneOf(['right', 'left']),
    iconClassName: PropTypes.string,
    icon: PropTypes.oneOfType([
      PropTypes.arrayOf(
        PropTypes.shape({
          name: PropTypes.string.isRequired,
          className: PropTypes.string,
        }),
      ),
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        className: PropTypes.string,
      }),
    ]),
    isForcedRight: PropTypes.bool,
    buttonClassName: PropTypes.string,
    noPull: PropTypes.bool,
    title: PropTypes.string,
    isKeepOpen: PropTypes.bool,
    isDisabled: PropTypes.bool,
    addExtraActions: PropTypes.func,
    ...withWindowScroll.props,
  };

  static defaultProps = {
    data: null,
    actions: [],
    footerRenderer: null,
    selectedId: null,
    isStrikeAction: false,
    hasLargeWidth: false,
    position: 'left',
    iconClassName: null,
    buttonClassName: null,
    isDisabled: false,
    icon: [
      {
        name: 'more',
        className: '',
      },
    ],
    isForcedRight: false,
    noPull: false,
    title: '',
    isKeepOpen: false,
  };

  constructor(props) {
    super(props);
    this.dropdown = React.createRef();
    this.state = {
      isOpen: false,
      dropup: false,
      menuContainerStyle: {},
    };
  }

  componentDidMount() {
    this.props.setScrollHandler(this.handleSizeChange);
  }

  componentDidUpdate(prevProps, prevState) {
    const { isOpen } = this.state;
    const { isOpen: wasOpen } = prevState;

    if (isOpen && !wasOpen) {
      this.props.setBodyClickHandler(this.handleClose, this.controlRef);
      this.calculateFixedBound();
    }

    if (!isOpen && wasOpen) {
      this.props.resetBodyClickHandler();
    }
  }

  handleSizeChange = e => {
    if (
      this.controlRef &&
      (e.target === window || e.target.contains(this.controlRef)) &&
      this.state.isOpen
    ) {
      this.calculateFixedBound(false);
    }
  };

  calculateFixedBound = () => {
    if (!this.controlRef || !window) {
      return;
    }

    let { position: forcePosition } = this.props;
    const { isForcedRight } = this.props;

    const button = this.controlRef.getBoundingClientRect();
    const { left, top, bottom, right } = button;

    const { width, height } = this.dropdown.current.getBoundingClientRect();

    const dropup = bottom + height > window.innerHeight;

    const forceLeft = right + width > window.innerWidth;
    const forceRight = left - width < 0;

    if (forceLeft) {
      forcePosition = 'left';
    } else if (forceRight) {
      forcePosition = 'right';
    }

    let leftContainer;
    const offset = 10;
    if (window.innerWidth < MAX_PAGE_WIDTH && isForcedRight) {
      leftContainer = '0px';
    } else if (forcePosition === 'left') {
      leftContainer = `${left - width + offset}px`;
    } else {
      leftContainer = `${right}px`;
    }

    const menuContainerStyle = {
      left: leftContainer,
      width: `${width}px`,
      top: dropup ? 'auto' : `${bottom}px`,
      bottom: dropup
        ? `${window.innerHeight - (height > top ? height : top)}px`
        : 'auto',
    };

    this.setState(() => ({ menuContainerStyle, dropup }));
  };

  handleClose = () => {
    if (this.props.isKeepOpen) {
      return;
    }

    this.setState({ isOpen: false });
  };

  handleClick = e => {
    const { isDisabled } = this.props;
    e.preventDefault();
    e.stopPropagation();

    if (!isDisabled) {
      this.setState(({ isOpen }) => ({ isOpen: !isOpen }));
    }
  };

  renderMenu = () => {
    const {
      actions,
      footerRenderer,
      data,
      selectedId,
      isStrikeAction,
      hasLargeWidth,
      iconClassName,
      addExtraActions,
    } = this.props;
    const { dropup, isOpen, menuContainerStyle } = this.state;

    if (!isOpen) {
      return;
    }
    const _actions = [...(addExtraActions ? addExtraActions(data) : actions)];

    return (
      <DropdownMenu
        _ref={this.dropdown}
        actions={_actions}
        data={data}
        dropup={dropup}
        footerRenderer={footerRenderer}
        hasLargeWidth={hasLargeWidth}
        iconClassName={iconClassName}
        isStrikeAction={isStrikeAction}
        menuContainerStyle={menuContainerStyle}
        selectedId={selectedId}
        onClose={this.handleClose}
      />
    );
  };

  render() {
    const { icon, position, buttonClassName, noPull, title } = this.props;

    return (
      <ul
        className={classnames('icons-list', {
          'pull-right': !noPull && position === 'right',
          'pull-left': !noPull && position === 'left',
        })}
        title={title}
      >
        <li ref={li => (this.controlRef = li)} className="open">
          <a
            className={classnames('text-default', buttonClassName)}
            href="#"
            title={title}
            onClick={this.handleClick}
          >
            {!icon && title}
            {icon &&
              _.isArray(icon) &&
              icon.map(icon => (
                <Icon
                  key={icon.name}
                  className={icon.className}
                  hoverTitle={title}
                  name={icon.name}
                  type={icon.type}
                />
              ))}
            {icon && !_.isArray(icon) && (
              <Icon
                key={icon.name}
                className={icon.className}
                hoverTitle={title}
                name={icon.name}
                type={icon.type}
              />
            )}
          </a>
          {this.renderMenu()}
        </li>
      </ul>
    );
  }
}
