import React from 'react';
import hoistNonReactStatics from 'hoist-non-react-statics';
import { debounce } from 'lodash';
import { Subtract } from 'utility-types/dist/mapped-types';

export const TOP_MENU_HEIGHT = 120;
export const MENU_PADDING = 30;
export const DEBOUNCE_TIME = 300;

type TState = {
  menuContainerStyle: object;
  height: string;
  dropup: boolean;
  width: number;
  maxHeight: number;
};

const withWindowScroll = <P extends IWithWindowScroll>(
  Component: React.ComponentType<P>,
) =>
  hoistNonReactStatics(
    class extends React.PureComponent<Subtract<P, IWithWindowScroll>, TState> {
      static displayName = `withWindowScroll(${
        Component.displayName || Component.name
      })`;

      state: TState = {
        menuContainerStyle: {},
        height: 'auto',
        dropup: false,
        width: 0,
        maxHeight: 0,
      };

      componentDidMount() {
        window.addEventListener('resize', this.handleScroll);
        window.addEventListener('scroll', this.handleScroll, true);
      }

      componentWillUnmount() {
        window.removeEventListener('resize', this.handleScroll);
        window.removeEventListener('scroll', this.handleScroll, true);
        this.resetBodyClickHandler();
        this.calculateDropupDebounced.cancel();
      }

      controlRef: HTMLElement | null = null;
      scrollHandler: Function | null = null;
      bodyHandler: Function | null = null;

      setScrollHandler = handler => {
        this.scrollHandler = handler;
      };

      setBodyClickHandler = (handler, controlRef) => {
        this.bodyHandler = handler;
        this.controlRef = controlRef;
        document.addEventListener('click', this.handleBodyClick);
      };

      resetBodyClickHandler = () => {
        this.bodyHandler = null;
        this.controlRef = null;
        document.removeEventListener('click', this.handleBodyClick);
      };

      handleScroll = e => {
        if (this.scrollHandler) {
          this.scrollHandler(e);
        }
      };

      handleBodyClick = e => {
        if (
          this.controlRef &&
          this.bodyHandler &&
          !this.controlRef.contains(e.target)
        ) {
          this.bodyHandler(e);
        }
      };

      calculateFixedBound = ({ setHeight, hasSearch, controlRef, style }) => {
        const { dropup } = this.state;
        if (!controlRef || !window) {
          return;
        }

        const button = controlRef.getBoundingClientRect();
        const { width, height, left, top, bottom } = button;

        const toTop = top;
        const toBottom = window.innerHeight - bottom;

        const menuContainerStyle = {
          ...style,
          left: `${left}px`,
          width: `${width}px`,
          top: dropup ? undefined : `${bottom}px`,
          bottom: dropup
            ? `${window.innerHeight - (height > top ? height : top)}px`
            : undefined,
        };

        this.setState(({ maxHeight }) => {
          if (setHeight) {
            maxHeight =
              Math.max(toTop, toBottom) - MENU_PADDING - TOP_MENU_HEIGHT;
            if (hasSearch) {
              // reduce size to one field with padding
              maxHeight -= height + MENU_PADDING;
            }
          }
          return { menuContainerStyle, maxHeight, width };
        });
      };

      calculateDropup = controlRef => {
        if (!controlRef || !window) {
          return;
        }

        const rect = controlRef.getBoundingClientRect();
        const { top, height } = rect;

        const toBottom = window.innerHeight - top - height;

        this.setState(() => ({
          dropup: top > toBottom,
        }));
      };

      calculateDropupDebounced = debounce(this.calculateDropup, DEBOUNCE_TIME);

      render() {
        return (
          <Component
            {...(this.props as P)}
            {...this.state}
            calculateDropup={this.calculateDropup}
            calculateDropupDebounced={this.calculateDropupDebounced}
            calculateFixedBound={this.calculateFixedBound}
            resetBodyClickHandler={this.resetBodyClickHandler}
            setBodyClickHandler={this.setBodyClickHandler}
            setScrollHandler={this.setScrollHandler}
          />
        );
      }
    },
    Component,
  );

export interface IWithWindowScroll {
  setScrollHandler: Function;
  setBodyClickHandler: Function;
  resetBodyClickHandler: Function;
  calculateFixedBound: Function;
  calculateDropup: Function;
  calculateDropupDebounced: Function;
  menuContainerStyle: object;
  height: string | number;
  width: string | number;
  dropup: boolean;
}

export default withWindowScroll;
